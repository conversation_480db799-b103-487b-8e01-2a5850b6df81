<template>
  <div class="layout-container">
    <!-- 顶部导航 -->
    <el-header class="header">
      <div class="header-content">
        <h1>语音合成系统管理后台</h1>
        <div class="user-info">
          <span>欢迎，{{ currentAdmin ? (currentAdmin.realName || currentAdmin.username) : '游客' }}</span>
          <el-dropdown @command="handleCommand">
            <span class="el-dropdown-link">
              <i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </el-header>

    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="240px" class="sidebar">
        <el-menu
          :default-active="$route.path"
          router
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          class="sidebar-menu">
          <el-menu-item index="/layout/dashboard">
            <i class="el-icon-s-platform"></i>
            <span slot="title">仪表板</span>
          </el-menu-item>
          <el-menu-item index="/layout/users">
            <i class="el-icon-user"></i>
            <span slot="title">用户管理</span>
          </el-menu-item>
          <el-menu-item index="/layout/synthesis">
            <i class="el-icon-microphone"></i>
            <span slot="title">合成管理</span>
          </el-menu-item>
          <el-menu-item index="/layout/points">
            <i class="el-icon-coin"></i>
            <span slot="title">积分管理</span>
          </el-menu-item>
          <el-menu-item index="/layout/settings">
            <i class="el-icon-setting"></i>
            <span slot="title">系统设置</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-main class="main-content">
        <router-view/>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Layout',
  computed: {
    ...mapGetters(['currentAdmin'])
  },
  methods: {
    handleCommand(command) {
      if (command === 'logout') {
        this.$store.dispatch('logout')
        this.$router.push('/login')
      }
    }
  }
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.header {
  background-color: #409EFF;
  color: white;
  padding: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.header-content h1 {
  margin: 0;
  font-size: 20px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.sidebar {
  background-color: #304156;
  min-height: calc(100vh - 60px);
}

.sidebar-menu {
  border-right: none;
  width: 100%;
}

.sidebar-menu .el-menu-item {
  height: 50px;
  line-height: 50px;
  padding: 0 20px !important;
}

.sidebar-menu .el-menu-item i {
  margin-right: 8px;
  font-size: 16px;
}

.sidebar-menu .el-menu-item span {
  font-size: 14px;
}

.main-content {
  padding: 20px;
  background-color: #f0f2f5;
}
</style>
